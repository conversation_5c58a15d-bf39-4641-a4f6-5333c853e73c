<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易机器人监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .admin-login-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .admin-login-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .admin-controls {
            background: #fff;
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            display: none;
        }
        
        .admin-controls h3 {
            color: #007bff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .control-group {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .control-group h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .control-group button {
            margin: 5px;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #218838; }
        
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #545b62; }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            color: #495057;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-content h3 {
            color: #495057;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-row {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-row label {
            min-width: 150px;
            font-weight: 500;
        }
        
        .form-row input, .form-row select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .config-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .config-section h4 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .modal-actions {
            margin-top: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
        }
        
        .modal-actions button {
            margin: 0 10px;
            padding: 10px 30px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .admin-controls {
                margin: 10px;
                padding: 15px;
            }
            
            .control-group button {
                width: 100%;
                margin: 5px 0;
            }
            
            .form-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .form-row label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <h1>📊 交易机器人监控面板</h1>
        <p>实时监控交易状态和系统性能</p>
        <button id="admin-login-btn" class="admin-login-btn" onclick="showAdminLogin()">
            🔧 管理员登录
        </button>
    </div>
    
    <!-- 管理员控制面板 -->
    <div id="admin-controls" class="admin-controls">
        <h3>🔧 管理员控制面板</h3>
        
        <!-- 系统控制 -->
        <div class="control-group">
            <h4>🎛️ 系统控制</h4>
            <button class="btn-success" onclick="startSystem()">🚀 启动系统</button>
            <button class="btn-danger" onclick="stopSystem()">🛑 停止系统</button>
            <button class="btn-warning" onclick="restartSystem()">🔄 重启系统</button>
        </div>
        
        <!-- 交易管理 -->
        <div class="control-group">
            <h4>⚡ 交易管理</h4>
            <button class="btn-primary" onclick="switchTradingMode()">🔄 切换交易模式</button>
            <button class="btn-primary" onclick="showConfigModal()">⚙️ 修改配置</button>
        </div>
        
        <!-- 系统管理 -->
        <div class="control-group">
            <h4>🔧 系统管理</h4>
            <button class="btn-secondary" onclick="cleanupLogs()">🧹 清理日志</button>
            <button class="btn-secondary" onclick="refreshContracts()">🔄 刷新合约</button>
        </div>
        
        <div style="margin-top: 20px; text-align: center; border-top: 1px solid #ddd; padding-top: 15px;">
            <button class="btn-secondary" onclick="adminLogout()">🚪 退出管理</button>
        </div>
    </div>
    
    <!-- 数据展示区域 -->
    <div class="dashboard">
        <div class="card">
            <h3>📈 系统状态</h3>
            <div id="system-status">
                <p>状态: <span id="status-text">加载中...</span></p>
                <p>运行时间: <span id="uptime">--</span></p>
                <p>CPU使用率: <span id="cpu-usage">--%</span></p>
                <p>内存使用率: <span id="memory-usage">--%</span></p>
            </div>
        </div>
        
        <div class="card">
            <h3>💰 交易统计</h3>
            <div id="trading-stats">
                <p>总交易次数: <span id="total-trades">--</span></p>
                <p>成功率: <span id="success-rate">--%</span></p>
                <p>总盈亏: <span id="total-pnl">-- USDT</span></p>
                <p>当前持仓: <span id="current-positions">--</span></p>
            </div>
        </div>
        
        <div class="card">
            <h3>📋 最新信号</h3>
            <div id="recent-signals">
                <p>加载中...</p>
            </div>
        </div>
        
        <div class="card">
            <h3>📊 交易记录</h3>
            <div id="trading-history">
                <p>加载中...</p>
            </div>
        </div>
    </div>
    
    <!-- 管理员登录弹窗 -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <h3>🔐 管理员登录</h3>
            <div class="form-group">
                <label>管理员密码:</label>
                <input type="password" id="admin-password" placeholder="请输入管理员密码" style="width: 100%; padding: 12px; margin-top: 10px;">
            </div>
            <div class="modal-actions">
                <button class="btn-primary" onclick="adminLogin()">🔑 登录</button>
                <button class="btn-secondary" onclick="closeLoginModal()">❌ 取消</button>
            </div>
        </div>
    </div>
    
    <!-- 配置修改弹窗 -->
    <div id="config-modal" class="modal">
        <div class="modal-content">
            <h3>⚙️ 系统配置</h3>

            <!-- 策略配置 -->
            <div class="config-section">
                <h4>🎯 交易策略</h4>
                <div class="form-row">
                    <label>交易金额 (USDT):</label>
                    <input type="number" id="strategy-amount" min="10" max="10000" step="10">
                </div>
                <div class="form-row">
                    <label>杠杆倍数:</label>
                    <select id="strategy-leverage">
                        <option value="5">5x</option>
                        <option value="10">10x</option>
                        <option value="15">15x</option>
                        <option value="20">20x</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>首次平仓时间 (秒):</label>
                    <input type="number" id="first-close-time" min="30" max="120">
                </div>
                <div class="form-row">
                    <label>首次平仓比例 (%):</label>
                    <input type="number" id="first-close-percentage" min="50" max="95" step="5">
                </div>
                <div class="form-row">
                    <label>回撤止损阈值 (%):</label>
                    <input type="number" id="drawdown-threshold" min="10" max="30">
                </div>
            </div>

            <!-- 交易模式 -->
            <div class="config-section">
                <h4>🔄 交易模式</h4>
                <div class="form-row">
                    <label>当前模式:</label>
                    <select id="trading-mode">
                        <option value="normal">普通交易</option>
                        <option value="copy_trading">跟单交易</option>
                    </select>
                </div>
                <div class="form-row">
                    <label>
                        <input type="checkbox" id="testnet-mode"> 使用测试网
                    </label>
                </div>
            </div>

            <!-- 通知设置 -->
            <div class="config-section">
                <h4>📢 通知设置</h4>
                <div class="form-row">
                    <label>
                        <input type="checkbox" id="feishu-enabled"> 启用飞书通知
                    </label>
                </div>
            </div>

            <div class="modal-actions">
                <button class="btn-primary" onclick="saveConfig()">💾 保存配置</button>
                <button class="btn-secondary" onclick="closeConfigModal()">❌ 取消</button>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>处理中，请稍候...</p>
    </div>

    <script src="/static/admin.js"></script>
</body>
</html>
