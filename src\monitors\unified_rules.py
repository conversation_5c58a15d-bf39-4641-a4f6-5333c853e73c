#!/usr/bin/env python3
"""
统一的监控规则配置
电报频道监控和刷新数据使用相同的规则
"""
import re
import requests
from datetime import datetime
from typing import List, Dict, Any
from loguru import logger

class UnifiedMonitoringRules:
    """统一的监控规则"""
    
    def __init__(self):
        # 统一的交易所关键词配置
        self.exchange_keywords = {
            'upbit': [
                'UPBIT LISTING:', 'UPBIT LISTING', 'Upbit 上新:', 'Upbit 上新: [交易]', 
                '[거래]', 'KRW 마켓'
            ],
            'binance': [
                'Binance Will Add'
            ],
            'coinbase': [
                'COINBASE LISTING:', 'COINBASE LISTING', 'COINBASE 上新'
            ],
            'bithumb': [
                'Bithumb Listing:', 'Bithumb Listing', 'Bithumb상장:', 'Bithumb上新:',
                '[마켓 추가]', '마켓 추가', '빗썸'
            ]
        }
        
        # 统一的排除关键词
        self.exclude_keywords = [
            '维护', '暂停', '停止', 'maintenance', 'suspend', 'delisting',
            '下架', 'delist', '暂停交易', '停止交易',
            'Token Swap', 'token swap', 'Rebranding', 'rebranding',
            'Token Swap and Rebranding'
        ]
        
        # 统一的排除词汇 - 只排除明确的非代币词汇
        self.exclude_words = {
            # 稳定币和主流币（避免重复提取）
            'USD', 'USDT', 'USDC', 'BTC', 'ETH', 'BNB',
            # 技术词汇
            'API', 'URL', 'HTTP', 'HTTPS', 'ERC', 'GMT', 'UTC', 'KST', 'EST', 'PST',
            # 交易所名称
            'BINANCE', 'UPBIT', 'COINBASE', 'BITHUMB', 'BYBIT',
            # 交易术语
            'LISTING', 'MARKET', 'SUPPORT', 'TRADING', 'EXCHANGE', 'FUTURES', 'SPOT',
            'DEPOSIT', 'WITHDRAWAL', 'TRADE', 'ORDER', 'LIMIT', 'STOP', 'LOSS',
            # 通用词汇（只保留明确的非代币词汇）
            'TELEGRAM', 'NETWORK', 'TOKEN', 'COIN', 'PROTOCOL',
            'PRODUCTS', 'ENJOY', 'APR', 'DAYS', 'SPECIAL', 'OFFER',
            'WILL', 'LAUNCH', 'AVAILABLE', 'USERS', 'CAN', 'START',
            'PLEASE', 'NOTE', 'RISK', 'WARNING', 'TERMS', 'CONDITIONS', 'SERVICE',
            'PRIVACY', 'POLICY', 'ANNOUNCEMENT', 'UPDATE', 'MAINTENANCE', 'SYSTEM',
            # 基础英文词汇（只保留绝对不可能是代币的）
            'AND', 'FOR', 'WITH', 'FROM', 'THIS'
        }
    
    def get_all_keywords(self) -> List[str]:
        """获取所有关键词的扁平列表（用于电报监控）"""
        all_keywords = []
        for exchange_keywords in self.exchange_keywords.values():
            all_keywords.extend(exchange_keywords)
        return list(set(all_keywords))  # 去重
    
    def detect_exchange_from_text(self, text: str) -> str:
        """从文本中检测交易所 - 固定策略无优先级差异"""
        text_lower = text.lower()

        # 检测交易所（无优先级顺序）
        for exchange, keywords in self.exchange_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return exchange

        return 'unknown'
    
    def is_listing_message(self, text: str) -> bool:
        """判断是否为上币消息"""
        text_lower = text.lower()
        
        # 排除关键词检查
        for exclude in self.exclude_keywords:
            if exclude.lower() in text_lower:
                return False
        
        # 检查是否包含任何交易所的上币关键词
        for exchange, keywords in self.exchange_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return True
        
        return False
    
    def extract_symbols_from_text(self, text: str) -> List[str]:
        """统一的代币符号提取逻辑"""
        
        # 首先尝试从链接中提取代币符号（最准确）
        link_tokens = self.extract_symbols_from_links(text)
        if link_tokens:
            return link_tokens
        
        # 如果没有链接，使用传统方法
        # 优先匹配括号内的字母数字组合（最可靠）
        pattern1 = r'\(([A-Z0-9]+)\)'
        tokens1 = re.findall(pattern1, text)

        # 匹配 $SYMBOL 格式（包含数字）
        pattern2 = r'\$([A-Z0-9]+)'
        tokens2 = re.findall(pattern2, text)

        # 匹配冒号后的代币符号（适用于中文消息）
        pattern_colon = r'[:：]\s*([A-Z0-9]+)\s*[\(（]'
        tokens_colon = re.findall(pattern_colon, text)

        # 匹配 "Add SYMBOL" 格式
        pattern_add = r'Add\s+([A-Z0-9]+)\s*[\(（]'
        tokens_add = re.findall(pattern_add, text)

        # 匹配 "LISTING: SYMBOL" 格式
        pattern_listing = r'LISTING:\s*([A-Z0-9]+)\s*[\(（]'
        tokens_listing = re.findall(pattern_listing, text)

        # 匹配通用格式 "SYMBOL (description)" - 作为后备
        pattern_general = r'\b([A-Z0-9]{1,10})\s*\([^)]+\)'
        tokens_general = re.findall(pattern_general, text)

        # 匹配Token Swap模式
        pattern_swap1 = r'(\w+)\s+will be rebranded to\s+(\w+)'
        tokens_swap1 = re.findall(pattern_swap1, text, re.IGNORECASE)
        tokens_swap1_flat = [token.upper() for match in tokens_swap1 for token in match]

        pattern_swap2 = r'(\w+)\s+will be swapped to\s+(\w+)'
        tokens_swap2 = re.findall(pattern_swap2, text, re.IGNORECASE)
        tokens_swap2_flat = [token.upper() for match in tokens_swap2 for token in match]

        # 合并并去重
        all_tokens = list(set(tokens1 + tokens2 + tokens_colon + tokens_add + tokens_listing + tokens_general + tokens_swap1_flat + tokens_swap2_flat))
        
        # 过滤（允许单字母和带数字的代币）
        filtered_tokens = []
        for token in all_tokens:
            if (token not in self.exclude_words and
                len(token) >= 1 and  # 允许单字母
                len(token) <= 10 and  # 最多10个字符
                token.isalnum() and  # 允许字母和数字的组合
                token.isupper()):  # 全大写
                filtered_tokens.append(token)
        
        return filtered_tokens
    
    def extract_symbols_from_links(self, text: str) -> List[str]:
        """从链接中提取代币符号"""
        try:
            # 匹配各种链接格式
            link_patterns = [
                r'https?://[^\s]+',  # 标准HTTP链接
                r'www\.[^\s]+',      # www链接
                r't\.me/[^\s]+',     # Telegram链接
            ]
            
            links = []
            for pattern in link_patterns:
                links.extend(re.findall(pattern, text))
            
            if not links:
                return []
            
            # 从链接中提取代币符号
            symbols = []
            for link in links:
                # 从URL路径中提取可能的代币符号
                link_upper = link.upper()
                
                # 匹配交易对格式 SYMBOL-USDT, SYMBOL-BTC等
                trading_pairs = re.findall(r'/([A-Z]+)-(?:USDT|USD|BTC|ETH|BNB|BUSD)', link_upper)
                symbols.extend(trading_pairs)
                
                # 匹配 /trade/SYMBOL 格式
                trade_tokens = re.findall(r'/TRADE/([A-Z]+)', link_upper)
                symbols.extend(trade_tokens)
                
                # 匹配 /trading/SYMBOL 格式
                trading_tokens = re.findall(r'/TRADING/([A-Z]+)', link_upper)
                symbols.extend(trading_tokens)
                
                # 匹配查询参数中的代币
                query_tokens = re.findall(r'[?&](?:symbol|coin|token)=([A-Z]+)', link_upper)
                symbols.extend(query_tokens)
            
            # 去重并过滤
            unique_symbols = list(set(symbols))
            filtered_symbols = []
            
            for symbol in unique_symbols:
                if (symbol not in self.exclude_words and
                    len(symbol) >= 1 and
                    len(symbol) <= 10 and
                    symbol.isalnum() and  # 允许字母和数字的组合
                    symbol.isupper()):  # 全大写
                    filtered_symbols.append(symbol)
            
            return filtered_symbols
            
        except Exception as e:
            logger.debug(f"从链接提取代币符号失败: {e}")
            return []
    
    def extract_marketcap_from_text(self, text: str, symbols: List[str]) -> Dict[str, str]:
        """从文本中提取MarketCap信息"""
        marketcaps = {}
        
        for symbol in symbols:
            try:
                # 匹配 $SYMBOL MarketCap: $XXXXm 格式
                pattern1 = rf'\${symbol}\s+MarketCap:\s*\$([0-9]+(?:\.[0-9]+)?[KMB]?)'
                match1 = re.search(pattern1, text, re.IGNORECASE)
                if match1:
                    marketcaps[symbol] = match1.group(1)
                    continue
                
                # 匹配其他格式
                pattern2 = rf'{symbol}.*?MarketCap:\s*\$([0-9]+(?:\.[0-9]+)?[KMB]?)'
                match2 = re.search(pattern2, text, re.IGNORECASE)
                if match2:
                    marketcaps[symbol] = match2.group(1)
                    
            except Exception as e:
                logger.debug(f"提取{symbol}的MarketCap失败: {e}")
        
        return marketcaps
    


# 创建全局实例
unified_rules = UnifiedMonitoringRules()
