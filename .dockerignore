# Python缓存
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.bak
*.swp
*.swo

# 配置备份
*.backup
config.yaml.backup

# 测试文件
test_*.py
*_test.py
tests/

# 文档
docs/
*.md
README.md

# 脚本工具
scripts/
examples/

# 临时脚本
diagnose_api.py
test_real_api.py
test_api_permissions.py
reset_system.py
setup_api_keys.py
start_demo.py
quick_start.py
update_config.py

# Git相关
.git/
.gitignore

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# 部署脚本
deploy_*.sh
fix_ssl.sh

# 配置模板
config_templates/
configs/
