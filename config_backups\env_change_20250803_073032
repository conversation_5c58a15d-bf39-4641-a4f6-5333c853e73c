# 智能交易机器人环境变量配置模板
# 复制此文件为 .env 并填入真实值

# Binance API配置
BINANCE_API_KEY=vaiGFrmItkZ0iZqHOjHXFxrGk5Pk21oWblhh3rZZMTjR0f5zGAzbyO8Cc1N6Lmc0
BINANCE_API_SECRET=Lm2ED1ahqFBKeKRAFRzUjkqL7jcDRDVhZv56Cfgi1qw8S7mpn351hEznjXASml5R

# Telegram配置
TELEGRAM_API_ID=26145597
TELEGRAM_API_HASH=859206f58db62ec957089a7e9ff11d38
TELEGRAM_PHONE=+8613375386798

# 系统配置
TZ=Asia/Shanghai
LOG_LEVEL=INFO
PYTHONPATH=/app
PYTHONUNBUFFERED=1

# Web服务配置
WEB_HOST=0.0.0.0
WEB_PORT=8080

# 数据库配置
DB_PATH=/app/data

# 缓存配置（可选）
REDIS_URL=redis://redis:6379/0

# 安全配置
SECRET_KEY=your_secret_key_here

# 调试模式（生产环境设为false）
DEBUG=false
