"""
代币黑名单管理器
动态管理Token Swap和Rebranding的代币黑名单
"""
import json
import re
from datetime import datetime, timedelta
from typing import Set, List, Dict, Optional
from pathlib import Path
from loguru import logger

class TokenBlacklistManager:
    """代币黑名单管理器"""
    
    def __init__(self, blacklist_file: str = "token_blacklist.json"):
        self.blacklist_file = Path(blacklist_file)
        self.blacklisted_tokens: Set[str] = set()
        self.blacklist_history: List[Dict] = []
        self.token_swap_keywords = [
            "token swap", "Token Swap", "TOKEN SWAP",
            "rebranding", "Rebranding", "REBRANDING", 
            "Token Swap and Rebranding",
            "will be rebranded", "rebranded to",
            "swap to", "migrate to", "replaced by"
        ]
        
        # 加载现有黑名单
        self.load_blacklist()
        
        logger.info(f"🚫 代币黑名单管理器初始化完成，当前黑名单: {len(self.blacklisted_tokens)} 个代币")
    
    def load_blacklist(self):
        """加载黑名单文件"""
        try:
            if self.blacklist_file.exists():
                with open(self.blacklist_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.blacklisted_tokens = set(data.get('tokens', []))
                    self.blacklist_history = data.get('history', [])
                    
                    # 清理过期的黑名单项（30天后自动移除）
                    self._cleanup_expired_tokens()
                    
                    logger.info(f"📋 加载黑名单: {len(self.blacklisted_tokens)} 个代币")
            else:
                logger.info("📋 黑名单文件不存在，创建新的黑名单")
                
        except Exception as e:
            logger.error(f"❌ 加载黑名单失败: {e}")
            self.blacklisted_tokens = set()
            self.blacklist_history = []
    
    def save_blacklist(self):
        """保存黑名单到文件"""
        try:
            data = {
                'tokens': list(self.blacklisted_tokens),
                'history': self.blacklist_history,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug(f"💾 黑名单已保存: {len(self.blacklisted_tokens)} 个代币")
            
        except Exception as e:
            logger.error(f"❌ 保存黑名单失败: {e}")
    
    def _cleanup_expired_tokens(self):
        """清理过期的黑名单代币（30天后自动移除）"""
        try:
            cutoff_date = datetime.now() - timedelta(days=30)
            
            # 找出过期的历史记录
            expired_records = []
            for record in self.blacklist_history:
                record_date = datetime.fromisoformat(record['timestamp'])
                if record_date < cutoff_date:
                    expired_records.append(record)
            
            # 移除过期代币
            for record in expired_records:
                tokens = record.get('tokens', [])
                for token in tokens:
                    if token in self.blacklisted_tokens:
                        self.blacklisted_tokens.remove(token)
                        logger.info(f"🗑️ 移除过期黑名单代币: {token}")
                
                self.blacklist_history.remove(record)
            
            if expired_records:
                self.save_blacklist()
                
        except Exception as e:
            logger.error(f"❌ 清理过期代币失败: {e}")
    
    def is_token_swap_message(self, text: str) -> bool:
        """检查是否为Token Swap消息"""
        text_lower = text.lower()
        
        for keyword in self.token_swap_keywords:
            if keyword.lower() in text_lower:
                return True
        
        return False
    
    def extract_tokens_from_swap_message(self, text: str) -> List[str]:
        """从Token Swap消息中提取代币符号"""
        tokens = []
        
        try:
            # 代币符号提取模式 - 按优先级排序
            patterns = [
                # 高优先级：精确的swap模式
                r'(\w+)\s+will be rebranded to\s+(\w+)',  # MATIC will be rebranded to POL
                r'(\w+)\s+will be swapped to\s+(\w+)',   # OLD will be swapped to NEW
                r'token swap[:\s]+(\w+)\s+to\s+(\w+)',   # Token Swap: OLD to NEW
                r'(\w+)\s+to\s+(\w+)\s+at\s+\d+:\d+',   # OLD to NEW at 1:1
                r'(\w+)\s*[→\->\s]+\s*(\w+)',           # MATIC → POL

                # 中优先级：括号格式（通常比较准确）
                r'\(([A-Z]{2,10})\)',                    # (MATIC)

                # 低优先级：通用大写字母（最后使用，容易误匹配）
                r'\b([A-Z]{2,10})\b',                    # 任何大写字母组合
            ]
            
            # 定义需要过滤的词汇 - 只过滤明确的非代币词汇
            def is_common_word(word):
                """判断是否为常见英文单词（非代币）"""
                word_upper = word.upper()

                # 明确的非代币词汇
                non_token_words = {
                    # 基础词汇
                    'THE', 'AND', 'FOR', 'WITH', 'FROM', 'WILL', 'BEEN', 'WERE', 'WAS',
                    'ARE', 'CAN', 'HAS', 'HAVE', 'THIS', 'THAT', 'THEY', 'YOUR',
                    'ALL', 'NOT', 'BUT', 'WE', 'YOU', 'BEFORE', 'AFTER',
                    # 常见介词和连词
                    'TO', 'BE', 'BY', 'AT', 'ON', 'IN', 'OF', 'OR', 'AS', 'AN', 'IS',
                    # Token Swap相关词汇
                    'TOKEN', 'TOKENS', 'SWAP', 'SWAPPED', 'SWAPPING', 'NOTICE',
                    'RATIO', 'PLEASE', 'DEADLINE', 'AUTOMATICALLY',
                    # 动作词汇
                    'REBRANDED', 'REPLACED', 'MIGRATE', 'MIGRATED', 'MIGRATING',
                    'REBRANDING', 'REPLACING', 'TRADING', 'EXCHANGE',
                    # 月份（完整名称）
                    'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'JUNE',
                    'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER',
                    # 其他明确的非代币词
                    'IMPORTANT', 'OFFICIAL', 'ANNOUNCEMENT', 'LISTING'
                }

                # 检查是否为明确的非代币词汇
                if word_upper in non_token_words:
                    return True

                # 检查是否为纯数字
                if word.isdigit():
                    return True

                # 检查是否为年份（4位数字）
                if len(word) == 4 and word.isdigit():
                    return True

                return False

            # 先尝试精确模式（前5个模式）
            for pattern in patterns[:5]:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    for group in match.groups():
                        if (group and len(group) >= 1 and len(group) <= 10 and  # 允许单字母
                            not is_common_word(group)):  # 使用新的过滤函数
                            tokens.append(group.upper())

            # 如果精确模式没有找到代币，再使用通用模式
            if not tokens:
                for pattern in patterns[5:]:
                    matches = re.finditer(pattern, text, re.IGNORECASE)
                    for match in matches:
                        for group in match.groups():
                            if (group and len(group) >= 1 and len(group) <= 10 and  # 允许单字母
                                not is_common_word(group)):  # 使用新的过滤函数
                                tokens.append(group.upper())
            
            # 去重并返回
            unique_tokens = list(set(tokens))
            logger.debug(f"🔍 从Token Swap消息中提取代币: {unique_tokens}")
            
            return unique_tokens
            
        except Exception as e:
            logger.error(f"❌ 提取代币符号失败: {e}")
            return []
    
    def add_tokens_to_blacklist(self, tokens: List[str], source_message: str):
        """将代币添加到黑名单"""
        if not tokens:
            return
        
        new_tokens = []
        for token in tokens:
            token = token.upper().strip()
            if token and token not in self.blacklisted_tokens:
                self.blacklisted_tokens.add(token)
                new_tokens.append(token)
        
        if new_tokens:
            # 记录历史
            record = {
                'timestamp': datetime.now().isoformat(),
                'tokens': new_tokens,
                'source_message': source_message[:200],  # 限制长度
                'reason': 'Token Swap/Rebranding'
            }
            self.blacklist_history.append(record)
            
            # 保存到文件
            self.save_blacklist()
            
            logger.warning(f"🚫 添加代币到黑名单: {new_tokens}")
            logger.info(f"📝 来源消息: {source_message[:100]}...")
    
    def is_token_blacklisted(self, token: str) -> bool:
        """检查代币是否在黑名单中"""
        return token.upper().strip() in self.blacklisted_tokens
    
    def process_message(self, text: str) -> Optional[List[str]]:
        """
        处理消息，如果是Token Swap消息则提取代币并加入黑名单
        返回提取到的代币列表，如果不是Token Swap消息则返回None
        """
        if self.is_token_swap_message(text):
            tokens = self.extract_tokens_from_swap_message(text)
            if tokens:
                self.add_tokens_to_blacklist(tokens, text)
                return tokens
        
        return None
    
    def get_blacklist_info(self) -> Dict:
        """获取黑名单信息"""
        return {
            'total_tokens': len(self.blacklisted_tokens),
            'tokens': list(self.blacklisted_tokens),
            'recent_additions': self.blacklist_history[-10:] if self.blacklist_history else []
        }
    
    def remove_token_from_blacklist(self, token: str) -> bool:
        """手动从黑名单中移除代币"""
        token = token.upper().strip()
        if token in self.blacklisted_tokens:
            self.blacklisted_tokens.remove(token)
            
            # 记录移除历史
            record = {
                'timestamp': datetime.now().isoformat(),
                'tokens': [token],
                'reason': 'Manual removal',
                'action': 'removed'
            }
            self.blacklist_history.append(record)
            
            self.save_blacklist()
            logger.info(f"✅ 手动移除黑名单代币: {token}")
            return True
        
        return False
    
    def clear_blacklist(self):
        """清空黑名单（谨慎使用）"""
        self.blacklisted_tokens.clear()
        self.blacklist_history.append({
            'timestamp': datetime.now().isoformat(),
            'reason': 'Manual clear all',
            'action': 'cleared'
        })
        self.save_blacklist()
        logger.warning("🗑️ 已清空所有黑名单代币")

# 全局实例
_blacklist_manager = None

def get_blacklist_manager() -> TokenBlacklistManager:
    """获取全局黑名单管理器实例"""
    global _blacklist_manager
    if _blacklist_manager is None:
        _blacklist_manager = TokenBlacklistManager()
    return _blacklist_manager
