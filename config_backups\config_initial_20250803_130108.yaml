# 智能交易机器人配置文件 - 精简版
# 多源监控，单点交易：监控4个交易所信号，在Binance执行固定策略

# 固定交易策略配置
fixed_strategy:
  enabled: true
  amount: 100  # 固定金额 100 USDT
  leverage: 10  # 10倍杠杆
  position_mode: isolated  # 逐仓模式
  first_close_time: 56  # 56秒后平仓80%
  first_close_percentage: 80  # 平仓80%
  monitor_duration: 300  # 5分钟监控期
  drawdown_threshold: 20  # 20%回撤阈值

# 日志配置
logging:
  file: trading_bot.log
  level: INFO
  max_size: 10MB
  backup_count: 5
  cleanup_time: "02:00"  # 每天凌晨2点清理日志
  dir: logs
  max_days: 10

# Telegram监控配置
monitoring:
  telegram:
    api_id: 26145597
    api_hash: "859206f58db62ec957089a7e9ff11d38"
    phone: "+8613375386798"
    channels:
      - "@BWEnews"
      - "@binance_announcements"
    # 精确的上币关键词 - 按交易所分类
    keywords:
      # Upbit关键词
      - "UPBIT LISTING:"
      - "UPBIT LISTING"
      - "Upbit 上新:"
      - "Upbit 上新: [交易]"
      - "[거래]"
      - "KRW 마켓"
      # Binance关键词
      - "Binance Will Add"
      # Coinbase关键词
      - "COINBASE LISTING:"
      - "COINBASE LISTING"
      - "COINBASE 上新"
      # Bithumb关键词
      - "Bithumb Listing:"
      - "Bithumb Listing"
      - "Bithumb상장:"
      - "Bithumb上新:"
      - "[마켓 추가]"
      - "마켓 추가"
      - "빗썸"
    # 排除关键词 - 避免误触发
    exclude_keywords:
      - "维护"
      - "暂停"
      - "停止"
      - "maintenance"
      - "suspend"
      - "delisting"
      - "下架"
      - "delist"
      - "暂停交易"
      - "停止交易"
      - "Token Swap"
      - "token swap"
      - "Rebranding"
      - "rebranding"
      - "Token Swap and Rebranding"

# 通知配置
notifications:
  feishu:
    enabled: true
    webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/f38e8064-fbed-401e-96b1-ff5d7df44bbb"
    webhook_sign: ""  # 可选的签名密钥

# Binance交易配置（唯一交易执行平台）
trading:
  binance:
    api_key: "vaiGFrmItkZ0iZqHOjHXFxrGk5Pk21oWblhh3rZZMTjR0f5zGAzbyO8Cc1N6Lmc0"
    api_secret: "Lm2ED1ahqFBKeKRAFRzUjkqL7jcDRDVhZv56Cfgi1qw8S7mpn351hEznjXASml5R"
    testnet: false  # 使用真实交易
    enabled: true
  mode: normal  # 交易模式: normal/copy_trading

# 跟单交易配置
copy_trading:
  enabled: false              # 是否启用跟单功能
  api_key: ""                 # 跟单专用API密钥 (可与普通API相同)
  api_secret: ""              # 跟单专用API密钥 (可与普通API相同)
  testnet: false              # 是否使用测试网

  # 交易参数
  amount: 100.0               # 跟单交易金额
  leverage: 10                # 跟单杠杆倍数

  # 带单者配置
  leader_config:
    min_follow_amount: 10     # 最小跟单金额
    max_follow_amount: 1000   # 最大跟单金额
    max_followers: 100        # 最大跟单者数量
    commission_rate: 0.1      # 分成比例 (10%)

# 风险控制配置
risk_control:
  signal_cooldown: 300  # 信号冷却时间 5分钟
  max_errors: 10  # 最大错误次数

# 系统配置
system:
  health_check_interval: 60
  timezone: "Asia/Shanghai"

# Web管理界面配置
web:
  enabled: true
  host: 0.0.0.0
  port: 8081

# Web安全配置
web_security:
  enabled: true
  admin_password: "TeeM00.87go"  # 管理员密码
  token_expire_hours: 24  # 令牌过期时间（小时）
  admin_ip_whitelist: []  # 管理员IP白名单，空表示允许所有IP
